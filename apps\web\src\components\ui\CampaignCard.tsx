"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { client } from "@/lib/trpc/client";
import {
  DocumentTextIcon,
  GiftIcon,
  UserPlusIcon,
} from "@heroicons/react/24/outline";

import { SerializedBrandProfile } from "@repo/server/src/types/brand";
import {
  ApplicationStatus,
  SerializedCampaign,
} from "@repo/server/src/types/campaign";
import { ContractStatus } from "@repo/server/src/types/contract";
import { DeliverableType } from "@repo/server/src/types/deliverable";

import { Button } from "./button";
import { CampaignApplicationModal } from "./CampaignApplicationModal";

interface Props {
  campaign: SerializedCampaign;
  applicationStatus?: ApplicationStatus;
  contractStatus?: ContractStatus;
  refetchCampaigns: () => void;
  refetchApplications: () => void;
  brandView?: boolean;
  hideApplyButton?: boolean;
  athleteView?: boolean;
}

export default function CampaignCard({
  campaign,
  applicationStatus,
  contractStatus,
  refetchCampaigns,
  refetchApplications,
  brandView = false,
  hideApplyButton = false,
  athleteView = false,
}: Props) {
  const [isApplicationModalOpen, setIsApplicationModalOpen] = useState(false);
  const [brand, setBrand] = useState<SerializedBrandProfile | null>(null);

  const daysToComplete = Math.ceil(
    (new Date(campaign.endDate).getTime() - new Date().getTime()) /
      (1000 * 60 * 60 * 24),
  );

  useEffect(() => {
    const fetchBrand = async () => {
      try {
        const brand = await client.brand.getBrand.query(campaign.brandId);
        setBrand(brand as SerializedBrandProfile);
      } catch (error) {
        console.error("Failed to fetch brand:", error);
        setBrand(null);
      }
    };
    fetchBrand();
  }, [campaign.brandId]);

  const handleOpenModal = () => {
    setIsApplicationModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsApplicationModalOpen(false);
  };

  const handleApplicationArea = () => {
    if (brandView || hideApplyButton) {
      return null;
    }
    const base = (applicationStatus: ApplicationStatus, color: string) => (
      <div
        className={`tw-mt-4 tw-text-aims-text-secondary tw-text-sm tw-text-center tw-border tw-border-aims-dark-3 tw-rounded-lg tw-p-3 sm:tw-p-2 tw-min-h-[44px] tw-flex tw-items-center tw-justify-center ${color}`}
      >
        Your application is {applicationStatus.toLowerCase()}.
      </div>
    );
    if (applicationStatus === ApplicationStatus.PENDING) {
      return base(ApplicationStatus.PENDING, "tw-bg-gray-500");
    } else if (applicationStatus === ApplicationStatus.ACCEPTED) {
      return base(ApplicationStatus.ACCEPTED, "tw-bg-green-500");
    } else if (applicationStatus === ApplicationStatus.REJECTED) {
      return base(ApplicationStatus.REJECTED, "tw-bg-red-500");
    } else if (applicationStatus === ApplicationStatus.WITHDRAWN) {
      return base(ApplicationStatus.WITHDRAWN, "tw-bg-gray-500");
    } else {
      return (
        <Button
          variant="outline"
          className="tw-mt-4 tw-w-full tw-text-aims-text-primary tw-gap-2 tw-h-12 sm:tw-h-10 tw-text-sm sm:tw-text-base"
          onClick={handleOpenModal}
          disabled={brandView}
        >
          <UserPlusIcon className="tw-w-5 tw-h-5" />
          Apply to participate
        </Button>
      );
    }
  };

  const payment = campaign.deliverables.reduce(
    (sum, d) => sum + d.minimumPayment,
    0,
  );

  // Find the gifted collaboration deliverable if it exists
  const giftedDeliverable = campaign.deliverables.find(
    (d) => d.type === DeliverableType.GIFTED_COLLABORATION,
  );
  return (
    <>
      <div className="tw-bg-aims-dark-2 tw-rounded-xl tw-shadow-lg tw-overflow-hidden tw-relative">
        {contractStatus === ContractStatus.FULFILLED && (
          <div className="tw-absolute tw-top-2 tw-right-2 tw-z-10 tw-bg-green-500 tw-text-white tw-text-xs tw-font-medium tw-px-2 tw-py-1 tw-rounded-full tw-flex tw-items-center tw-gap-1">
            ✓ Completed
          </div>
        )}
        <div className="tw-relative tw-w-full tw-h-40 sm:tw-h-48">
          <Image
            src={brand?.logo.url || "/no-profile-pic.jpg"}
            alt="Campaign"
            fill
            className="tw-object-cover"
          />
        </div>
        <div className="tw-p-3 sm:tw-p-4">
          <div className="tw-text-aims-text-secondary tw-text-base sm:tw-text-lg">
            {brand?.companyName}
            <a
              href={`/app/brand/${campaign.brandId}`}
              className="tw-text-aims-primary tw-text-sm tw-ml-2 sm:tw-ml-3 tw-inline-flex tw-items-center tw-min-h-[44px]"
            >
              View brand profile
            </a>
          </div>
          <div className="tw-text-2xl sm:tw-text-3xl tw-mt-2 tw-text-aims-text-primary tw-leading-tight">
            {campaign.name}
          </div>
          <div className="tw-text-3xl sm:tw-text-4xl tw-mt-3 tw-text-aims-text-primary tw-font-bold">
            ${payment.toFixed(2)}{" "}
            <span className="tw-text-xs tw-text-aims-text-secondary tw-block sm:tw-inline">
              {giftedDeliverable ? (
                <span>
                  + <GiftIcon className="tw-w-4 tw-h-4 tw-inline" />
                </span>
              ) : (
                ""
              )}
            </span>
          </div>
          <div className="tw-mt-4 tw-bg-aims-dark-3 tw-text-aims-text-secondary tw-text-sm tw-rounded tw-p-3 sm:tw-p-4 tw-flex tw-flex-col tw-justify-between tw-gap-3 sm:tw-gap-4">
            <div className="tw-flex tw-flex-row tw-justify-between tw-items-center">
              <div>Days to complete</div>
              <div className="tw-font-medium">
                {daysToComplete} day{daysToComplete !== 1 ? "s" : ""}
              </div>
            </div>
            <div className="tw-flex tw-flex-row tw-justify-between tw-items-center">
              <div>Deliverables</div>
              <div className="tw-font-medium">{campaign.deliverables.length}</div>
            </div>
          </div>
          {handleApplicationArea()}
          <Link
            href={`/app/campaign/${campaign.id}?brandView=${brandView}`}
            className={`tw-mt-3 tw-w-full tw-gap-2 tw-flex tw-items-center tw-justify-center tw-py-3 sm:tw-py-2 tw-rounded-lg tw-transition-colors tw-min-h-[44px] tw-text-sm sm:tw-text-base ${contractStatus === ContractStatus.AWAITING_DELIVERABLES ? 'tw-bg-aims-primary tw-text-black hover:tw-bg-aims-primary/80' : 'tw-bg-transparent tw-text-aims-text-primary hover:tw-bg-aims-dark-3'}`}
          >
            <DocumentTextIcon className="tw-w-5 tw-h-5" />
            {contractStatus === ContractStatus.FULFILLED
              ? "View Completed Campaign"
              : athleteView
                ? "Submit Deliverables"
                : "View Details"
            }
          </Link>
        </div>
      </div>

      <CampaignApplicationModal
        campaign={campaign}
        isOpen={isApplicationModalOpen}
        onClose={handleCloseModal}
        refetchCampaigns={refetchCampaigns}
        refetchApplications={refetchApplications}
      />
    </>
  );
}
